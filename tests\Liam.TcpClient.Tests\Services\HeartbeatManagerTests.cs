using FluentAssertions;
using Liam.TcpClient.Interfaces;
using Liam.TcpClient.Models;
using Liam.TcpClient.Services;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Liam.TcpClient.Tests.Services;

/// <summary>
/// HeartbeatManager 测试
/// </summary>
public class HeartbeatManagerTests : IDisposable
{
    private readonly Mock<ILogger<HeartbeatManager>> _mockLogger;
    private readonly Mock<IMessageHandler> _mockMessageHandler;
    private readonly HeartbeatManager _heartbeatManager;

    public HeartbeatManagerTests()
    {
        _mockLogger = new Mock<ILogger<HeartbeatManager>>();
        _mockMessageHandler = new Mock<IMessageHandler>();
        _heartbeatManager = new HeartbeatManager(_mockLogger.Object, _mockMessageHandler.Object);
    }

    [Fact]
    public void Constructor_ShouldInitializeCorrectly()
    {
        // Arrange & Act
        var manager = new HeartbeatManager(_mockLogger.Object, _mockMessageHandler.Object);

        // Assert
        manager.Should().NotBeNull();
        manager.IsEnabled.Should().BeFalse();
        manager.IsRunning.Should().BeFalse();
        manager.Interval.Should().Be(TimeSpan.FromSeconds(60)); // 默认值
        manager.Timeout.Should().Be(TimeSpan.FromSeconds(10)); // 默认值
    }

    [Fact]
    public void Constructor_WithNullLogger_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var act = () => new HeartbeatManager(null!, _mockMessageHandler.Object);
        act.Should().Throw<ArgumentNullException>().WithParameterName("logger");
    }

    [Fact]
    public void SetInterval_WithValidTimeSpan_ShouldUpdateInterval()
    {
        // Arrange
        var newInterval = TimeSpan.FromSeconds(30);

        // Act
        _heartbeatManager.Interval = newInterval;

        // Assert
        _heartbeatManager.Interval.Should().Be(newInterval);
    }

    [Fact]
    public void SetTimeout_WithValidTimeSpan_ShouldUpdateTimeout()
    {
        // Arrange
        var newTimeout = TimeSpan.FromSeconds(15);

        // Act
        _heartbeatManager.Timeout = newTimeout;

        // Assert
        _heartbeatManager.Timeout.Should().Be(newTimeout);
    }

    [Theory]
    [InlineData(-1)]
    [InlineData(0)]
    public void SetInterval_WithInvalidTimeSpan_ShouldThrowArgumentException(int seconds)
    {
        // Arrange
        var invalidInterval = TimeSpan.FromSeconds(seconds);

        // Act & Assert
        var act = () => _heartbeatManager.Interval = invalidInterval;
        act.Should().Throw<ArgumentException>();
    }

    [Theory]
    [InlineData(-1)]
    [InlineData(0)]
    public void SetTimeout_WithInvalidTimeSpan_ShouldThrowArgumentException(int seconds)
    {
        // Arrange
        var invalidTimeout = TimeSpan.FromSeconds(seconds);

        // Act & Assert
        var act = () => _heartbeatManager.Timeout = invalidTimeout;
        act.Should().Throw<ArgumentException>();
    }

    [Fact]
    public async Task StartAsync_ShouldEnableHeartbeat()
    {
        // Act
        await _heartbeatManager.StartAsync();

        // Assert
        _heartbeatManager.IsEnabled.Should().BeTrue();
        _heartbeatManager.IsRunning.Should().BeTrue();
    }

    [Fact]
    public async Task StartAsync_WhenAlreadyRunning_ShouldLogWarning()
    {
        // Arrange
        await _heartbeatManager.StartAsync();

        // Act
        await _heartbeatManager.StartAsync();

        // Assert
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Warning,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("心跳管理器已在运行")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task StopAsync_ShouldDisableHeartbeat()
    {
        // Arrange
        await _heartbeatManager.StartAsync();

        // Act
        await _heartbeatManager.StopAsync();

        // Assert
        _heartbeatManager.IsEnabled.Should().BeFalse();
        _heartbeatManager.IsRunning.Should().BeFalse();
    }

    [Fact]
    public async Task StopAsync_WhenNotRunning_ShouldCompleteSuccessfully()
    {
        // Act
        var act = async () => await _heartbeatManager.StopAsync();

        // Assert
        await act.Should().NotThrowAsync();
    }

    [Fact]
    public async Task SendHeartbeatAsync_WhenEnabled_ShouldSendHeartbeat()
    {
        // Arrange
        _mockMessageHandler.Setup(x => x.SendMessageAsync(It.IsAny<TcpMessage>(), It.IsAny<CancellationToken>()))
                          .ReturnsAsync(true);
        await _heartbeatManager.StartAsync();

        // Act
        var result = await _heartbeatManager.SendHeartbeatAsync();

        // Assert
        result.Should().BeTrue();
        _mockMessageHandler.Verify(x => x.SendMessageAsync(It.IsAny<TcpMessage>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task SendHeartbeatAsync_WhenDisabled_ShouldReturnFalse()
    {
        // Act
        var result = await _heartbeatManager.SendHeartbeatAsync();

        // Assert
        result.Should().BeFalse();
        _mockMessageHandler.Verify(x => x.SendMessageAsync(It.IsAny<TcpMessage>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task SendHeartbeatAsync_WhenMessageHandlerFails_ShouldReturnFalse()
    {
        // Arrange
        _mockMessageHandler.Setup(x => x.SendMessageAsync(It.IsAny<TcpMessage>(), It.IsAny<CancellationToken>()))
                          .ReturnsAsync(false);
        await _heartbeatManager.StartAsync();

        // Act
        var result = await _heartbeatManager.SendHeartbeatAsync();

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task SendHeartbeatAsync_WhenMessageHandlerThrows_ShouldReturnFalse()
    {
        // Arrange
        _mockMessageHandler.Setup(x => x.SendMessageAsync(It.IsAny<TcpMessage>(), It.IsAny<CancellationToken>()))
                          .ThrowsAsync(new InvalidOperationException("Test exception"));
        await _heartbeatManager.StartAsync();

        // Act
        var result = await _heartbeatManager.SendHeartbeatAsync();

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task StartAsync_AfterDispose_ShouldThrowObjectDisposedException()
    {
        // Arrange
        _heartbeatManager.Dispose();

        // Act & Assert
        var act = async () => await _heartbeatManager.StartAsync();
        await act.Should().ThrowAsync<ObjectDisposedException>();
    }

    [Fact]
    public void Dispose_ShouldDisposeResourcesCorrectly()
    {
        // Act
        var act = () => _heartbeatManager.Dispose();

        // Assert
        act.Should().NotThrow();
    }

    [Fact]
    public async Task DisposeAsync_ShouldDisposeResourcesCorrectly()
    {
        // Act
        var act = async () => await _heartbeatManager.DisposeAsync();

        // Assert
        await act.Should().NotThrowAsync();
    }

    [Fact]
    public async Task HeartbeatEvents_ShouldBeTriggeredCorrectly()
    {
        // Arrange
        var heartbeatSentEventTriggered = false;

        _heartbeatManager.HeartbeatSent += (sender, args) => heartbeatSentEventTriggered = true;

        _mockMessageHandler.Setup(x => x.SendMessageAsync(It.IsAny<TcpMessage>(), It.IsAny<CancellationToken>()))
                          .ReturnsAsync(true);

        await _heartbeatManager.StartAsync();

        // Act
        await _heartbeatManager.SendHeartbeatAsync();

        // Assert
        heartbeatSentEventTriggered.Should().BeTrue();
        // 注意：HeartbeatReceived 和 HeartbeatTimeout 需要特定条件触发，这里主要测试事件订阅机制
    }

    public void Dispose()
    {
        _heartbeatManager?.Dispose();
    }
}
