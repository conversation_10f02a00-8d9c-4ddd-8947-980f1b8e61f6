using FluentAssertions;
using Liam.TcpClient.Constants;
using Liam.TcpClient.Models;
using Liam.TcpClient.Services;
using Microsoft.Extensions.Logging;
using Moq;
using System.Text;
using Xunit;

namespace Liam.TcpClient.Tests.Services;

/// <summary>
/// MessageHandler 测试
/// </summary>
public class MessageHandlerTests : IDisposable
{
    private readonly Mock<ILogger<MessageHandler>> _mockLogger;
    private readonly MessageHandler _messageHandler;
    private readonly MemoryStream _memoryStream;

    public MessageHandlerTests()
    {
        _mockLogger = new Mock<ILogger<MessageHandler>>();
        _messageHandler = new MessageHandler(_mockLogger.Object);
        _memoryStream = new MemoryStream();
    }

    [Fact]
    public void Constructor_ShouldInitializeCorrectly()
    {
        // Arrange & Act
        var handler = new MessageHandler(_mockLogger.Object);

        // Assert
        handler.Should().NotBeNull();
        handler.IsRunning.Should().BeFalse();
    }

    [Fact]
    public void Constructor_WithNullLogger_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var act = () => new MessageHandler(null!);
        act.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public async Task StartAsync_WithValidStream_ShouldStartSuccessfully()
    {
        // Act
        await _messageHandler.StartAsync(_memoryStream);

        // Assert
        _messageHandler.IsRunning.Should().BeTrue();
    }

    [Fact]
    public async Task StartAsync_WithNullStream_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var act = async () => await _messageHandler.StartAsync(null!);
        await act.Should().ThrowAsync<ArgumentNullException>().WithParameterName("stream");
    }

    [Fact]
    public async Task StartAsync_WhenAlreadyRunning_ShouldLogWarning()
    {
        // Arrange
        await _messageHandler.StartAsync(_memoryStream);

        // Act
        await _messageHandler.StartAsync(_memoryStream);

        // Assert
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Warning,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("消息处理器已在运行")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task StopAsync_ShouldStopSuccessfully()
    {
        // Arrange
        await _messageHandler.StartAsync(_memoryStream);

        // Act
        await _messageHandler.StopAsync();

        // Assert
        _messageHandler.IsRunning.Should().BeFalse();
    }

    [Fact]
    public async Task StopAsync_WhenNotRunning_ShouldCompleteSuccessfully()
    {
        // Act
        var act = async () => await _messageHandler.StopAsync();

        // Assert
        await act.Should().NotThrowAsync();
    }

    [Fact]
    public async Task SendAsync_WithValidData_ShouldReturnTrue()
    {
        // Arrange
        var data = Encoding.UTF8.GetBytes("Hello World");
        await _messageHandler.StartAsync(_memoryStream);

        // Act
        var result = await _messageHandler.SendAsync(data);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task SendAsync_WithNullData_ShouldThrowArgumentNullException()
    {
        // Arrange
        await _messageHandler.StartAsync(_memoryStream);

        // Act & Assert
        var act = async () => await _messageHandler.SendAsync(null!);
        await act.Should().ThrowAsync<ArgumentNullException>().WithParameterName("data");
    }

    [Fact]
    public async Task SendAsync_WithEmptyData_ShouldReturnTrue()
    {
        // Arrange
        var data = Array.Empty<byte>();
        await _messageHandler.StartAsync(_memoryStream);

        // Act
        var result = await _messageHandler.SendAsync(data);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task SendAsync_WhenNotRunning_ShouldReturnFalse()
    {
        // Arrange
        var data = Encoding.UTF8.GetBytes("Hello World");

        // Act
        var result = await _messageHandler.SendAsync(data);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task SendTextAsync_WithValidText_ShouldReturnTrue()
    {
        // Arrange
        var text = "Hello World";
        var data = Encoding.UTF8.GetBytes(text);
        await _messageHandler.StartAsync(_memoryStream);

        // Act
        var result = await _messageHandler.SendAsync(data);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task SendTextAsync_WithNullText_ShouldThrowArgumentNullException()
    {
        // Arrange
        await _messageHandler.StartAsync(_memoryStream);

        // Act & Assert
        var act = async () => await _messageHandler.SendAsync(null!);
        await act.Should().ThrowAsync<ArgumentNullException>().WithParameterName("data");
    }

    [Fact]
    public async Task SendTextAsync_WithEmptyText_ShouldReturnTrue()
    {
        // Arrange
        var data = Array.Empty<byte>();
        await _messageHandler.StartAsync(_memoryStream);

        // Act
        var result = await _messageHandler.SendAsync(data);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task SendMessageAsync_WithValidMessage_ShouldReturnTrue()
    {
        // Arrange
        var message = TcpMessage.CreateTextMessage("Hello World");
        await _messageHandler.StartAsync(_memoryStream);

        // Act
        var result = await _messageHandler.SendMessageAsync(message);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task SendMessageAsync_WithNullMessage_ShouldThrowArgumentNullException()
    {
        // Arrange
        await _messageHandler.StartAsync(_memoryStream);

        // Act & Assert
        var act = async () => await _messageHandler.SendMessageAsync(null!);
        await act.Should().ThrowAsync<ArgumentNullException>().WithParameterName("message");
    }

    [Fact]
    public async Task ReceiveAsync_WithTimeout_ShouldReturnNullOnTimeout()
    {
        // Arrange
        await _messageHandler.StartAsync(_memoryStream);
        var timeout = TimeSpan.FromMilliseconds(100);

        // Act
        var result = await _messageHandler.ReceiveAsync(timeout);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task ReceiveTextAsync_WithTimeout_ShouldReturnNullOnTimeout()
    {
        // Arrange
        await _messageHandler.StartAsync(_memoryStream);
        var timeout = TimeSpan.FromMilliseconds(100);

        // Act
        var result = await _messageHandler.ReceiveAsync(timeout);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task ReceiveMessageAsync_WithTimeout_ShouldReturnNullOnTimeout()
    {
        // Arrange
        await _messageHandler.StartAsync(_memoryStream);
        var timeout = TimeSpan.FromMilliseconds(100);

        // Act
        var result = await _messageHandler.ReceiveMessageAsync(timeout);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task MessageEvents_ShouldBeTriggeredCorrectly()
    {
        // Arrange
        await _messageHandler.StartAsync(_memoryStream);

        // Act
        var message = TcpMessage.CreateTextMessage("Test");
        var result = await _messageHandler.SendMessageAsync(message);

        // Assert
        result.Should().BeTrue();
        // 注意：具体的事件测试需要根据实际的MessageHandler实现来调整
    }

    [Fact]
    public async Task StartAsync_AfterDispose_ShouldThrowObjectDisposedException()
    {
        // Arrange
        _messageHandler.Dispose();

        // Act & Assert
        var act = async () => await _messageHandler.StartAsync(_memoryStream);
        await act.Should().ThrowAsync<ObjectDisposedException>();
    }

    [Fact]
    public void Dispose_ShouldDisposeResourcesCorrectly()
    {
        // Act
        var act = () => _messageHandler.Dispose();

        // Assert
        act.Should().NotThrow();
    }

    [Fact]
    public async Task DisposeAsync_ShouldDisposeResourcesCorrectly()
    {
        // Act
        var act = async () => await _messageHandler.DisposeAsync();

        // Assert
        await act.Should().NotThrowAsync();
    }

    [Theory]
    [InlineData(1024)]
    [InlineData(8192)]
    [InlineData(65536)]
    public async Task SendAsync_WithLargeData_ShouldHandleCorrectly(int dataSize)
    {
        // Arrange
        var data = new byte[dataSize];
        new Random().NextBytes(data);
        await _messageHandler.StartAsync(_memoryStream);

        // Act
        var result = await _messageHandler.SendAsync(data);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task SendAsync_WithCancellationToken_ShouldRespectCancellation()
    {
        // Arrange
        var data = Encoding.UTF8.GetBytes("Hello World");
        await _messageHandler.StartAsync(_memoryStream);
        using var cts = new CancellationTokenSource();
        cts.Cancel();

        // Act & Assert
        var act = async () => await _messageHandler.SendAsync(data, cts.Token);
        await act.Should().ThrowAsync<OperationCanceledException>();
    }

    public void Dispose()
    {
        _messageHandler?.Dispose();
        _memoryStream?.Dispose();
    }
}
