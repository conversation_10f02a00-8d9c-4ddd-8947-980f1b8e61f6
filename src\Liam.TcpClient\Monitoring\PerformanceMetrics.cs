using System.Collections.Concurrent;
using System.Diagnostics;

namespace Liam.TcpClient.Monitoring;

/// <summary>
/// 性能指标收集器
/// </summary>
public class PerformanceMetrics
{
    private readonly ConcurrentDictionary<string, PerformanceCounter> _counters = new();
    private readonly ConcurrentDictionary<string, long> _totalCounts = new();
    private readonly ConcurrentDictionary<string, double> _totalTimes = new();
    private readonly object _lock = new();

    /// <summary>
    /// 记录操作执行时间
    /// </summary>
    /// <param name="operationName">操作名称</param>
    /// <param name="elapsedMilliseconds">执行时间（毫秒）</param>
    public void RecordOperationTime(string operationName, double elapsedMilliseconds)
    {
        ArgumentNullException.ThrowIfNull(operationName);

        _totalCounts.AddOrUpdate(operationName, 1, (_, count) => count + 1);
        _totalTimes.AddOrUpdate(operationName, elapsedMilliseconds, (_, total) => total + elapsedMilliseconds);
    }

    /// <summary>
    /// 增加计数器
    /// </summary>
    /// <param name="counterName">计数器名称</param>
    /// <param name="increment">增量</param>
    public void IncrementCounter(string counterName, long increment = 1)
    {
        ArgumentNullException.ThrowIfNull(counterName);

        _totalCounts.AddOrUpdate(counterName, increment, (_, count) => count + increment);
    }

    /// <summary>
    /// 获取操作的平均执行时间
    /// </summary>
    /// <param name="operationName">操作名称</param>
    /// <returns>平均执行时间（毫秒）</returns>
    public double GetAverageOperationTime(string operationName)
    {
        ArgumentNullException.ThrowIfNull(operationName);

        if (!_totalTimes.TryGetValue(operationName, out var totalTime) ||
            !_totalCounts.TryGetValue(operationName, out var count) ||
            count == 0)
        {
            return 0;
        }

        return totalTime / count;
    }

    /// <summary>
    /// 获取计数器值
    /// </summary>
    /// <param name="counterName">计数器名称</param>
    /// <returns>计数器值</returns>
    public long GetCounterValue(string counterName)
    {
        ArgumentNullException.ThrowIfNull(counterName);

        return _totalCounts.TryGetValue(counterName, out var count) ? count : 0;
    }

    /// <summary>
    /// 获取所有性能指标
    /// </summary>
    /// <returns>性能指标字典</returns>
    public Dictionary<string, object> GetAllMetrics()
    {
        var metrics = new Dictionary<string, object>();

        // 添加计数器
        foreach (var kvp in _totalCounts)
        {
            metrics[$"Count_{kvp.Key}"] = kvp.Value;
        }

        // 添加平均时间
        foreach (var kvp in _totalTimes)
        {
            if (_totalCounts.TryGetValue(kvp.Key, out var count) && count > 0)
            {
                metrics[$"AvgTime_{kvp.Key}"] = kvp.Value / count;
            }
        }

        return metrics;
    }

    /// <summary>
    /// 重置所有指标
    /// </summary>
    public void Reset()
    {
        lock (_lock)
        {
            _counters.Clear();
            _totalCounts.Clear();
            _totalTimes.Clear();
        }
    }

    /// <summary>
    /// 创建性能计时器
    /// </summary>
    /// <param name="operationName">操作名称</param>
    /// <returns>性能计时器</returns>
    public PerformanceTimer CreateTimer(string operationName)
    {
        return new PerformanceTimer(this, operationName);
    }
}

/// <summary>
/// 性能计时器
/// </summary>
public class PerformanceTimer : IDisposable
{
    private readonly PerformanceMetrics _metrics;
    private readonly string _operationName;
    private readonly Stopwatch _stopwatch;
    private bool _disposed;

    internal PerformanceTimer(PerformanceMetrics metrics, string operationName)
    {
        _metrics = metrics;
        _operationName = operationName;
        _stopwatch = Stopwatch.StartNew();
    }

    /// <summary>
    /// 停止计时并记录结果
    /// </summary>
    public void Stop()
    {
        if (!_disposed && _stopwatch.IsRunning)
        {
            _stopwatch.Stop();
            _metrics.RecordOperationTime(_operationName, _stopwatch.Elapsed.TotalMilliseconds);
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            Stop();
            _disposed = true;
        }
    }
}

/// <summary>
/// 性能计数器
/// </summary>
public class PerformanceCounter
{
    private long _value;
    private readonly object _lock = new();

    /// <summary>
    /// 当前值
    /// </summary>
    public long Value => _value;

    /// <summary>
    /// 增加计数
    /// </summary>
    /// <param name="increment">增量</param>
    public void Increment(long increment = 1)
    {
        lock (_lock)
        {
            _value += increment;
        }
    }

    /// <summary>
    /// 减少计数
    /// </summary>
    /// <param name="decrement">减量</param>
    public void Decrement(long decrement = 1)
    {
        lock (_lock)
        {
            _value -= decrement;
        }
    }

    /// <summary>
    /// 重置计数器
    /// </summary>
    public void Reset()
    {
        lock (_lock)
        {
            _value = 0;
        }
    }

    /// <summary>
    /// 设置值
    /// </summary>
    /// <param name="value">新值</param>
    public void Set(long value)
    {
        lock (_lock)
        {
            _value = value;
        }
    }
}

/// <summary>
/// 连接质量监控器
/// </summary>
public class ConnectionQualityMonitor
{
    private readonly PerformanceMetrics _metrics;
    private readonly Queue<DateTime> _connectionAttempts = new();
    private readonly Queue<DateTime> _successfulConnections = new();
    private readonly Queue<double> _latencyHistory = new();
    private readonly object _lock = new();

    /// <summary>
    /// 初始化连接质量监控器
    /// </summary>
    /// <param name="metrics">性能指标实例</param>
    public ConnectionQualityMonitor(PerformanceMetrics metrics)
    {
        _metrics = metrics ?? throw new ArgumentNullException(nameof(metrics));
    }

    /// <summary>
    /// 记录连接尝试
    /// </summary>
    public void RecordConnectionAttempt()
    {
        lock (_lock)
        {
            var now = DateTime.UtcNow;
            _connectionAttempts.Enqueue(now);
            
            // 保留最近5分钟的数据
            while (_connectionAttempts.Count > 0 && 
                   (now - _connectionAttempts.Peek()).TotalMinutes > 5)
            {
                _connectionAttempts.Dequeue();
            }
        }

        _metrics.IncrementCounter("ConnectionAttempts");
    }

    /// <summary>
    /// 记录成功连接
    /// </summary>
    public void RecordSuccessfulConnection()
    {
        lock (_lock)
        {
            var now = DateTime.UtcNow;
            _successfulConnections.Enqueue(now);
            
            // 保留最近5分钟的数据
            while (_successfulConnections.Count > 0 && 
                   (now - _successfulConnections.Peek()).TotalMinutes > 5)
            {
                _successfulConnections.Dequeue();
            }
        }

        _metrics.IncrementCounter("SuccessfulConnections");
    }

    /// <summary>
    /// 记录延迟
    /// </summary>
    /// <param name="latencyMs">延迟（毫秒）</param>
    public void RecordLatency(double latencyMs)
    {
        lock (_lock)
        {
            _latencyHistory.Enqueue(latencyMs);
            
            // 保留最近100个延迟记录
            if (_latencyHistory.Count > 100)
            {
                _latencyHistory.Dequeue();
            }
        }

        _metrics.RecordOperationTime("Latency", latencyMs);
    }

    /// <summary>
    /// 获取连接成功率
    /// </summary>
    /// <returns>连接成功率（0-1）</returns>
    public double GetConnectionSuccessRate()
    {
        lock (_lock)
        {
            if (_connectionAttempts.Count == 0)
                return 1.0;

            return (double)_successfulConnections.Count / _connectionAttempts.Count;
        }
    }

    /// <summary>
    /// 获取平均延迟
    /// </summary>
    /// <returns>平均延迟（毫秒）</returns>
    public double GetAverageLatency()
    {
        lock (_lock)
        {
            if (_latencyHistory.Count == 0)
                return 0;

            return _latencyHistory.Average();
        }
    }

    /// <summary>
    /// 获取连接质量评分
    /// </summary>
    /// <returns>质量评分（0-100）</returns>
    public int GetQualityScore()
    {
        var successRate = GetConnectionSuccessRate();
        var avgLatency = GetAverageLatency();

        // 基于成功率和延迟计算质量评分
        var successScore = successRate * 60; // 最高60分
        var latencyScore = Math.Max(0, 40 - (avgLatency / 10)); // 延迟越低分数越高，最高40分

        return (int)Math.Min(100, successScore + latencyScore);
    }
}
