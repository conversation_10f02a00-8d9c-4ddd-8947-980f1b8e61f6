using FluentAssertions;
using Liam.TcpClient.Exceptions;
using Liam.TcpClient.Interfaces;
using Liam.TcpClient.Models;
using Microsoft.Extensions.Logging;
using Moq;
using System.Net.Sockets;
using Xunit;

namespace Liam.TcpClient.Tests.Services;

/// <summary>
/// 异常处理和错误恢复测试
/// </summary>
public class ErrorRecoveryTests : IDisposable
{
    private readonly TcpClientConfig _config;

    public ErrorRecoveryTests()
    {
        _config = new TcpClientConfig
        {
            Host = "localhost",
            Port = 8080,
            EnableAutoReconnect = true,
            MaxReconnectAttempts = 3,
            ReconnectIntervalSeconds = 1
        };
    }

    [Fact]
    public async Task ConnectAsync_WithNetworkException_ShouldHandleGracefully()
    {
        // Arrange
        var mockClient = new Mock<ITcpClient>();
        mockClient.Setup(x => x.ConnectAsync(It.IsAny<CancellationToken>()))
                  .ThrowsAsync(new SocketException((int)SocketError.NetworkUnreachable));

        // Act & Assert
        var act = async () => await mockClient.Object.ConnectAsync();
        await act.Should().ThrowAsync<SocketException>();
    }

    [Fact]
    public async Task ConnectAsync_WithTimeoutException_ShouldHandleGracefully()
    {
        // Arrange
        var mockClient = new Mock<ITcpClient>();
        mockClient.Setup(x => x.ConnectAsync(It.IsAny<CancellationToken>()))
                  .ThrowsAsync(new TimeoutException("Connection timeout"));

        // Act & Assert
        var act = async () => await mockClient.Object.ConnectAsync();
        await act.Should().ThrowAsync<TimeoutException>();
    }

    [Fact]
    public async Task SendAsync_WithConnectionLost_ShouldHandleException()
    {
        // Arrange
        var mockClient = new Mock<ITcpClient>();
        mockClient.Setup(x => x.SendAsync(It.IsAny<byte[]>(), It.IsAny<CancellationToken>()))
                  .ThrowsAsync(new SocketException((int)SocketError.ConnectionReset));

        var data = System.Text.Encoding.UTF8.GetBytes("test");

        // Act & Assert
        var act = async () => await mockClient.Object.SendAsync(data);
        await act.Should().ThrowAsync<SocketException>();
    }

    [Fact]
    public async Task SendMessageAsync_WithInvalidMessage_ShouldHandleGracefully()
    {
        // Arrange
        var mockClient = new Mock<ITcpClient>();
        mockClient.Setup(x => x.SendMessageAsync(It.IsAny<TcpMessage>(), It.IsAny<CancellationToken>()))
                  .ThrowsAsync(new MessageException("Invalid message format"));

        var message = TcpMessage.CreateTextMessage("test");

        // Act & Assert
        var act = async () => await mockClient.Object.SendMessageAsync(message);
        await act.Should().ThrowAsync<MessageException>();
    }

    [Fact]
    public async Task ReceiveAsync_WithStreamException_ShouldHandleGracefully()
    {
        // Arrange
        var mockClient = new Mock<ITcpClient>();
        mockClient.Setup(x => x.ReceiveAsync(It.IsAny<TimeSpan?>(), It.IsAny<CancellationToken>()))
                  .ThrowsAsync(new InvalidOperationException("Stream closed"));

        // Act & Assert
        var act = async () => await mockClient.Object.ReceiveAsync();
        await act.Should().ThrowAsync<InvalidOperationException>();
    }

    [Fact]
    public async Task ReconnectAsync_WithException_ShouldHandleGracefully()
    {
        // Arrange
        var mockClient = new Mock<ITcpClient>();
        mockClient.Setup(x => x.ReconnectAsync(It.IsAny<CancellationToken>()))
                  .ThrowsAsync(new SocketException((int)SocketError.ConnectionRefused));

        // Act & Assert
        var act = async () => await mockClient.Object.ReconnectAsync();
        await act.Should().ThrowAsync<SocketException>();
    }

    [Fact]
    public void TcpClientConfig_Validation_ShouldWorkCorrectly()
    {
        // Arrange & Act
        var config = new TcpClientConfig
        {
            Host = "localhost",
            Port = 8080,
            EnableAutoReconnect = true,
            MaxReconnectAttempts = 3
        };

        // Assert
        config.Host.Should().Be("localhost");
        config.Port.Should().Be(8080);
        config.EnableAutoReconnect.Should().BeTrue();
        config.MaxReconnectAttempts.Should().Be(3);
    }

    [Fact]
    public void MessageException_ShouldCreateCorrectly()
    {
        // Arrange
        var message = "Test error message";

        // Act
        var exception = new MessageException(message);

        // Assert
        exception.Message.Should().Be(message);
        exception.Should().BeOfType<MessageException>();
    }

    public void Dispose()
    {
        // 清理测试资源
    }
}
