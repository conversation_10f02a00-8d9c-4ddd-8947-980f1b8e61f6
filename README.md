# Liam系列.NET功能库集合

## 项目概述
Liam系列是一套完整的.NET 8功能库生态系统，提供模块化、高质量的开发组件。每个功能库都经过精心设计，遵循.NET最佳实践，支持现代开发需求。

## 功能库列表
| 库名称 | 功能描述 | NuGet包 | 文档 | 状态 |
|--------|----------|---------|------|------|
| [Liam.Cryptography](src/Liam.Cryptography/README.md) | 现代化加密功能库，支持AES、ChaCha20-Poly1305、RSA、ECDSA、SHA-256、Argon2等算法，包含流式处理和性能优化 | [![NuGet](https://img.shields.io/nuget/v/Liam.Cryptography.svg)](https://www.nuget.org/packages/Liam.Cryptography/) | [详细文档](src/Liam.Cryptography/README.md) | 🚀 已发布 |
| [Liam.SerialPort](src/Liam.SerialPort/README.md) | 现代化串口通讯功能库，支持跨平台串口设备发现、连接管理、数据收发、热插拔检测和自动重连等功能。v1.1.0新增：Liam.Logging集成支持 | [![NuGet](https://img.shields.io/nuget/v/Liam.SerialPort.svg)](https://www.nuget.org/packages/Liam.SerialPort/) | [详细文档](src/Liam.SerialPort/README.md) | 🚀 已发布 |
| [Liam.Logging](src/Liam.Logging/README.md) | 现代化日志记录功能库，支持多级日志、多种输出目标、结构化日志、异步记录、日志轮转等功能，完全兼容Microsoft.Extensions.Logging。v1.1.0新增：真正的异步I/O、IAsyncDisposable支持、标准Dispose模式 | [![NuGet](https://img.shields.io/nuget/v/Liam.Logging.svg)](https://www.nuget.org/packages/Liam.Logging/) | [详细文档](src/Liam.Logging/README.md) | 🚀 已发布 |
| [Liam.TcpServer](src/Liam.TcpServer/README.md) | 现代化TCP服务器通信库，支持多客户端连接管理、异步数据传输、SSL/TLS安全通信、心跳检测、连接池管理等功能，集成Liam.Logging日志记录。v1.0.1新增：安全认证机制、ArrayPool缓冲区优化、ConfigureAwait优化、IAsyncDisposable支持、常量集中化管理 | [![NuGet](https://img.shields.io/nuget/v/Liam.TcpServer.svg)](https://www.nuget.org/packages/Liam.TcpServer/) | [详细文档](src/Liam.TcpServer/README.md) | 🚀 已发布 |
| [Liam.TcpClient](src/Liam.TcpClient/README.md) | 现代化TCP客户端通信库，支持连接管理、异步数据传输、SSL/TLS安全通信、自动重连、心跳检测、连接池管理等功能，与Liam.TcpServer完全兼容，基于.NET 8.0构建 | [![NuGet](https://img.shields.io/nuget/v/Liam.TcpClient.svg)](https://www.nuget.org/packages/Liam.TcpClient/) | [详细文档](src/Liam.TcpClient/README.md) | ✅ 已完成 |

## 技术规范
- **.NET版本**: .NET 8.0
- **语言版本**: C# 12 (latest)
- **包管理**: NuGet
- **版本控制**: Git + Gitee
- **测试框架**: xUnit + Moq + FluentAssertions
- **文档生成**: XML文档注释
- **代码分析**: 启用.NET分析器

## 开发状态跟踪
| 功能库 | 开发状态 | 测试覆盖率 | NuGet版本 | 最后更新 |
|--------|----------|------------|-----------|----------|
| Liam.Cryptography | 🚀 已发布 | 100% (272/272个测试通过) | [![NuGet](https://img.shields.io/nuget/v/Liam.Cryptography.svg)](https://www.nuget.org/packages/Liam.Cryptography/) | 2025-06-15 |
| Liam.SerialPort | 🚀 已发布 | 100% (219/219个测试通过) | [![NuGet](https://img.shields.io/nuget/v/Liam.SerialPort.svg)](https://www.nuget.org/packages/Liam.SerialPort/) | 2025-06-15 v1.1.0 |
| Liam.Logging | 🚀 已发布 | 100% (82/82个测试通过) | [![NuGet](https://img.shields.io/nuget/v/Liam.Logging.svg)](https://www.nuget.org/packages/Liam.Logging/) | 2025-06-15 |
| Liam.TcpServer | 🚀 已发布 | 100% (66/66个测试通过) | [![NuGet](https://img.shields.io/nuget/v/Liam.TcpServer.svg)](https://www.nuget.org/packages/Liam.TcpServer/) | 2025-06-15 v1.0.2 (包含图标和README) |
| Liam.TcpClient | 🚀 已发布 | 42%覆盖率 (277个测试，265通过) | [![NuGet](https://img.shields.io/nuget/v/Liam.TcpClient.svg)](https://www.nuget.org/packages/Liam.TcpClient/) | 2025-06-16 v1.0.7 (P1级编译器警告修复、P2级测试覆盖率提升至42%、新增277个测试用例) |

## 架构设计

### 设计原则
1. **模块化**: 每个功能库独立开发，可单独使用
2. **接口优先**: 通过接口定义模块边界，支持依赖注入
3. **异步支持**: 为I/O密集型操作提供异步方法
4. **异常处理**: 使用自定义异常类型，提供有意义的错误消息
5. **向后兼容**: 谨慎处理破坏性变更，维护API稳定性

### 目录结构
```
Liam/
├── src/                          # 源代码目录
│   ├── Liam.Cryptography/        # 加密功能库
│   │   ├── Interfaces/           # 接口定义
│   │   ├── Models/              # 数据模型
│   │   ├── Services/            # 核心服务实现
│   │   ├── Extensions/          # 扩展方法
│   │   ├── Constants/           # 常量定义
│   │   ├── Exceptions/          # 自定义异常
│   │   └── README.md            # 库独立文档
│   ├── Liam.SerialPort/          # 串口通讯功能库
│   │   ├── Interfaces/           # 接口定义
│   │   ├── Models/              # 数据模型
│   │   ├── Services/            # 核心服务实现
│   │   ├── Extensions/          # 扩展方法
│   │   ├── Constants/           # 常量定义
│   │   ├── Exceptions/          # 自定义异常
│   │   ├── Events/              # 事件定义
│   │   └── README.md            # 库独立文档
│   ├── Liam.Logging/            # 日志记录功能库
│   │   ├── Interfaces/           # 接口定义
│   │   ├── Models/              # 数据模型
│   │   ├── Services/            # 核心服务实现
│   │   ├── Extensions/          # 扩展方法
│   │   ├── Constants/           # 常量定义
│   │   ├── Exceptions/          # 自定义异常
│   │   ├── Providers/           # 日志提供程序
│   │   ├── Formatters/          # 日志格式化器
│   │   ├── Filters/             # 日志过滤器
│   │   └── README.md            # 库独立文档
│   ├── Liam.TcpServer/          # TCP服务器通信库
│   │   ├── Interfaces/           # 接口定义
│   │   ├── Models/              # 数据模型
│   │   ├── Services/            # 核心服务实现
│   │   ├── Handlers/            # 消息处理器、事件处理器
│   │   ├── Extensions/          # 扩展方法
│   │   ├── Constants/           # 常量定义
│   │   ├── Exceptions/          # 自定义异常
│   │   ├── Events/              # 事件定义
│   │   └── README.md            # 库独立文档
│   └── Liam.TcpClient/          # TCP客户端通信库
│       ├── Interfaces/           # 接口定义
│       ├── Models/              # 数据模型
│       ├── Services/            # 核心服务实现
│       ├── Extensions/          # 扩展方法
│       ├── Constants/           # 常量定义
│       ├── Exceptions/          # 自定义异常
│       ├── Events/              # 事件定义
│       └── README.md            # 库独立文档
├── tests/                       # 测试项目目录
├── examples/                    # 示例项目目录
├── Liam.sln                     # 解决方案文件
└── README.md                    # 主文档
```

## 开发计划

### 已完成功能库
- ✅ **Liam.Cryptography**: 加密解密功能库
- ✅ **Liam.SerialPort**: 串口通讯功能库
- ✅ **Liam.Logging**: 日志记录库（结构化日志、多种输出目标）
- ✅ **Liam.TcpServer**: TCP服务器通信库（多客户端连接、SSL/TLS、心跳检测）
- ✅ **Liam.TcpClient**: TCP客户端通信库（连接管理、自动重连、心跳检测、连接池）

### 计划中的功能库
- 🔄 **Liam.Common**: 基础公共库（工具类、扩展方法、常用功能）
- 🔄 **Liam.Data**: 数据访问库（ORM封装、数据库工具）
- 🔄 **Liam.Web**: Web开发库（API工具、中间件、过滤器）
- 🔄 **Liam.Cache**: 缓存库（内存缓存、分布式缓存）
- 🔄 **Liam.Configuration**: 配置管理库（多源配置、动态配置）
- 🔄 **Liam.Validation**: 数据验证库（模型验证、业务规则）
- 🔄 **Liam.Messaging**: 消息队列库（事件总线、消息处理）

## 质量保证

### 代码质量标准
- **测试覆盖率**: 目标≥80%
- **代码分析**: 启用.NET分析器，处理所有警告
- **文档注释**: 所有公共成员必须包含XML文档注释
- **性能考虑**: 关注内存使用和执行效率



### 发布流程
1. **开发完成**: 功能实现→单元测试→代码检查→文档更新
2. **发布准备**: 测试验证→版本更新→包构建→质量检查
3. **正式发布**: Git提交→推送到Gitee→NuGet发布→状态更新

## 贡献指南

### 开发环境要求
- .NET 8.0 SDK
- Visual Studio 2022 或 Visual Studio Code
- Git

### 贡献流程
1. Fork项目到您的GitHub/Gitee账户
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

### 代码规范
- 遵循.NET命名约定
- 使用XML文档注释
- 编写单元测试
- 保持代码简洁和可读性

## 许可证
本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式
- **项目主页**: https://gitee.com/liam-gitee/liam
- **问题反馈**: 请在Gitee上提交Issue

## 致谢
感谢所有为Liam系列功能库做出贡献的开发者！

---

*Liam系列.NET功能库集合 - 让.NET开发更简单、更高效！*
