using FluentAssertions;
using Liam.TcpClient.Extensions;
using Liam.TcpClient.Interfaces;
using Liam.TcpClient.Models;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using System.Collections.Concurrent;
using System.Diagnostics;
using Xunit;

namespace Liam.TcpClient.Tests.Performance;

/// <summary>
/// 并发和性能测试
/// </summary>
public class ConcurrencyTests : IDisposable
{
    private readonly Mock<ITcpClient> _mockClient;
    private readonly Mock<ITcpClientPool> _mockPool;
    private readonly Mock<ILogger<TcpClientPool>> _mockLogger;

    public ConcurrencyTests()
    {
        _mockClient = new Mock<ITcpClient>();
        _mockPool = new Mock<ITcpClientPool>();
        _mockLogger = new Mock<ILogger<TcpClientPool>>();
    }

    [Fact]
    public async Task SendBatchAsync_WithConcurrentMessages_ShouldHandleCorrectly()
    {
        // Arrange
        const int messageCount = 100;
        const int maxConcurrency = 10;
        
        var messages = Enumerable.Range(1, messageCount)
            .Select(i => TcpMessage.CreateTextMessage($"Message {i}"))
            .ToArray();

        var sendCount = 0;
        _mockClient.Setup(x => x.SendMessageAsync(It.IsAny<TcpMessage>(), It.IsAny<CancellationToken>()))
                   .Returns(async () =>
                   {
                       Interlocked.Increment(ref sendCount);
                       await Task.Delay(10); // 模拟网络延迟
                       return true;
                   });

        // Act
        var stopwatch = Stopwatch.StartNew();
        var result = await TcpClientExtensions.SendBatchAsync(_mockClient.Object, messages, maxConcurrency);
        stopwatch.Stop();

        // Assert
        result.Should().Be(messageCount);
        sendCount.Should().Be(messageCount);
        
        // 验证并发控制 - 应该比顺序发送快，但不会太快（说明有并发控制）
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(messageCount * 10); // 比顺序快
        stopwatch.ElapsedMilliseconds.Should().BeGreaterThan(50); // 但有一定的控制
    }

    [Fact]
    public async Task SendBatchTextAsync_WithHighConcurrency_ShouldMaintainOrder()
    {
        // Arrange
        const int textCount = 50;
        const int maxConcurrency = 20;
        
        var texts = Enumerable.Range(1, textCount)
            .Select(i => $"Text {i}")
            .ToArray();

        var receivedTexts = new ConcurrentBag<string>();
        _mockClient.Setup(x => x.SendTextAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                   .Returns<string, CancellationToken>(async (text, ct) =>
                   {
                       receivedTexts.Add(text);
                       await Task.Delay(Random.Shared.Next(1, 10), ct); // 随机延迟
                       return true;
                   });

        // Act
        var result = await TcpClientExtensions.SendBatchTextAsync(_mockClient.Object, texts, maxConcurrency);

        // Assert
        result.Should().Be(textCount);
        receivedTexts.Should().HaveCount(textCount);
        
        // 验证所有文本都被发送
        foreach (var text in texts)
        {
            receivedTexts.Should().Contain(text);
        }
    }

    [Fact]
    public async Task ConnectionPool_WithConcurrentRequests_ShouldHandleCorrectly()
    {
        // Arrange
        const int requestCount = 50;
        const int poolSize = 10;
        
        var clientsInUse = new ConcurrentBag<ITcpClient>();
        var maxConcurrentClients = 0;
        var currentConcurrentClients = 0;

        _mockPool.Setup(x => x.GetClientAsync(It.IsAny<CancellationToken>()))
               .Returns(async () =>
               {
                   var current = Interlocked.Increment(ref currentConcurrentClients);
                   if (current > maxConcurrentClients)
                   {
                       Interlocked.Exchange(ref maxConcurrentClients, current);
                   }
                   
                   await Task.Delay(10); // 模拟获取延迟
                   var client = new Mock<ITcpClient>().Object;
                   clientsInUse.Add(client);
                   return client;
               });

        _mockPool.Setup(x => x.ReturnClientAsync(It.IsAny<ITcpClient>()))
               .Returns(async (ITcpClient client) =>
               {
                   Interlocked.Decrement(ref currentConcurrentClients);
                   await Task.Delay(5); // 模拟归还延迟
               });

        // Act
        var tasks = Enumerable.Range(1, requestCount)
            .Select(async i =>
            {
                var client = await _mockPool.Object.GetClientAsync();
                await Task.Delay(Random.Shared.Next(10, 50)); // 模拟使用时间
                await _mockPool.Object.ReturnClientAsync(client);
            });

        await Task.WhenAll(tasks);

        // Assert
        clientsInUse.Should().HaveCount(requestCount);
        maxConcurrentClients.Should().BeLessThanOrEqualTo(poolSize);
        currentConcurrentClients.Should().Be(0); // 所有客户端都应该被归还
    }

    [Fact]
    public async Task MultipleClients_ConcurrentConnections_ShouldNotInterfere()
    {
        // Arrange
        const int clientCount = 20;
        var clients = new List<Mock<ITcpClient>>();
        var connectionResults = new ConcurrentBag<bool>();

        for (int i = 0; i < clientCount; i++)
        {
            var mockClient = new Mock<ITcpClient>();
            mockClient.Setup(x => x.ConnectAsync(It.IsAny<CancellationToken>()))
                     .Returns(async () =>
                     {
                         await Task.Delay(Random.Shared.Next(10, 100)); // 随机连接时间
                         var success = Random.Shared.NextDouble() > 0.1; // 90% 成功率
                         connectionResults.Add(success);
                         return success;
                     });
            clients.Add(mockClient);
        }

        // Act
        var connectionTasks = clients.Select(client => client.Object.ConnectAsync());
        await Task.WhenAll(connectionTasks);

        // Assert
        connectionResults.Should().HaveCount(clientCount);
        connectionResults.Count(r => r).Should().BeGreaterThan((int)(clientCount * 0.8)); // 至少80%成功
    }

    [Fact]
    public async Task SendReceive_HighFrequency_ShouldMaintainPerformance()
    {
        // Arrange
        const int operationCount = 1000;
        var operationTimes = new ConcurrentBag<long>();

        _mockClient.Setup(x => x.SendTextAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                   .Returns(async () =>
                   {
                       await Task.Delay(1); // 最小延迟
                       return true;
                   });

        _mockClient.Setup(x => x.ReceiveTextAsync(It.IsAny<TimeSpan?>(), It.IsAny<CancellationToken>()))
                   .Returns(async () =>
                   {
                       await Task.Delay(1); // 最小延迟
                       return "Response";
                   });

        // Act
        var tasks = Enumerable.Range(1, operationCount)
            .Select(async i =>
            {
                var stopwatch = Stopwatch.StartNew();
                
                await _mockClient.Object.SendTextAsync($"Message {i}");
                await _mockClient.Object.ReceiveTextAsync(TimeSpan.FromSeconds(1));
                
                stopwatch.Stop();
                operationTimes.Add(stopwatch.ElapsedMilliseconds);
            });

        var overallStopwatch = Stopwatch.StartNew();
        await Task.WhenAll(tasks);
        overallStopwatch.Stop();

        // Assert
        operationTimes.Should().HaveCount(operationCount);
        
        var averageTime = operationTimes.Average();
        var maxTime = operationTimes.Max();
        
        averageTime.Should().BeLessThan(50); // 平均操作时间应该小于50ms
        maxTime.Should().BeLessThan(200); // 最大操作时间应该小于200ms
        
        // 整体吞吐量检查
        var operationsPerSecond = operationCount * 1000.0 / overallStopwatch.ElapsedMilliseconds;
        operationsPerSecond.Should().BeGreaterThan(100); // 至少100 ops/sec
    }

    [Fact]
    public async Task CancellationToken_ConcurrentOperations_ShouldCancelCorrectly()
    {
        // Arrange
        const int operationCount = 100;
        using var cts = new CancellationTokenSource();
        
        var cancelledCount = 0;
        var completedCount = 0;

        _mockClient.Setup(x => x.SendTextAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                   .Returns(async (string text, CancellationToken ct) =>
                   {
                       try
                       {
                           await Task.Delay(100, ct); // 较长的操作时间
                           Interlocked.Increment(ref completedCount);
                           return true;
                       }
                       catch (OperationCanceledException)
                       {
                           Interlocked.Increment(ref cancelledCount);
                           throw;
                       }
                   });

        // Act
        var tasks = Enumerable.Range(1, operationCount)
            .Select(i => _mockClient.Object.SendTextAsync($"Message {i}", cts.Token));

        // 在50ms后取消操作
        _ = Task.Delay(50).ContinueWith(_ => cts.Cancel());

        try
        {
            await Task.WhenAll(tasks);
        }
        catch (OperationCanceledException)
        {
            // 预期的异常
        }

        // Assert
        cancelledCount.Should().BeGreaterThan(0); // 应该有一些操作被取消
        (cancelledCount + completedCount).Should().Be(operationCount); // 总数应该匹配
    }

    [Fact]
    public async Task MemoryUsage_UnderLoad_ShouldRemainStable()
    {
        // Arrange
        const int messageCount = 1000;
        var initialMemory = GC.GetTotalMemory(true);

        _mockClient.Setup(x => x.SendMessageAsync(It.IsAny<TcpMessage>(), It.IsAny<CancellationToken>()))
                   .ReturnsAsync(true);

        // Act
        for (int i = 0; i < messageCount; i++)
        {
            var message = TcpMessage.CreateTextMessage($"Large message with lots of data: {new string('x', 1000)}");
            await _mockClient.Object.SendMessageAsync(message);
            
            if (i % 100 == 0)
            {
                GC.Collect(); // 定期强制垃圾回收
                GC.WaitForPendingFinalizers();
            }
        }

        var finalMemory = GC.GetTotalMemory(true);

        // Assert
        var memoryIncrease = finalMemory - initialMemory;
        memoryIncrease.Should().BeLessThan(10 * 1024 * 1024); // 内存增长应该小于10MB
    }

    public void Dispose()
    {
        // 清理测试资源
    }
}
