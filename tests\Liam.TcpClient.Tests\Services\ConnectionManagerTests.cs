using FluentAssertions;
using Liam.TcpClient.Models;
using Liam.TcpClient.Services;
using Microsoft.Extensions.Logging;
using Moq;
using System.Net;
using System.Net.Sockets;
using Xunit;

namespace Liam.TcpClient.Tests.Services;

/// <summary>
/// ConnectionManager 测试
/// </summary>
public class ConnectionManagerTests : IDisposable
{
    private readonly Mock<ILogger<ConnectionManager>> _mockLogger;
    private readonly ConnectionManager _connectionManager;
    private readonly TcpClientConfig _config;

    public ConnectionManagerTests()
    {
        _mockLogger = new Mock<ILogger<ConnectionManager>>();
        _connectionManager = new ConnectionManager(_mockLogger.Object);
        
        _config = new TcpClientConfig
        {
            Host = "localhost",
            Port = 8080,
            ConnectionTimeoutSeconds = 5,
            ReceiveBufferSize = 8192,
            SendBufferSize = 8192
        };
    }

    [Fact]
    public void Constructor_ShouldInitializeCorrectly()
    {
        // Arrange & Act
        var manager = new ConnectionManager(_mockLogger.Object);

        // Assert
        manager.Should().NotBeNull();
        manager.IsConnected.Should().BeFalse();
        manager.IsConnecting.Should().BeFalse();
        manager.ConnectionInfo.Should().BeNull();
    }

    [Fact]
    public void IsConnected_WhenNotConnected_ShouldReturnFalse()
    {
        // Act & Assert
        _connectionManager.IsConnected.Should().BeFalse();
    }

    [Fact]
    public void IsConnecting_WhenNotConnecting_ShouldReturnFalse()
    {
        // Act & Assert
        _connectionManager.IsConnecting.Should().BeFalse();
    }

    [Fact]
    public async Task ConnectAsync_WithNullHost_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var act = async () => await _connectionManager.ConnectAsync(null!, 8080, _config);
        await act.Should().ThrowAsync<ArgumentNullException>().WithParameterName("host");
    }

    [Fact]
    public async Task ConnectAsync_WithNullConfig_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var act = async () => await _connectionManager.ConnectAsync("localhost", 8080, null!);
        await act.Should().ThrowAsync<ArgumentNullException>().WithParameterName("config");
    }

    [Fact]
    public async Task ConnectAsync_WithInvalidPort_ShouldReturnFalse()
    {
        // Arrange
        var invalidConfig = new TcpClientConfig
        {
            Host = "localhost",
            Port = 99999, // 无效端口
            ConnectionTimeoutSeconds = 1
        };

        // Act
        var result = await _connectionManager.ConnectAsync("localhost", 99999, invalidConfig);

        // Assert
        result.Should().BeFalse();
        _connectionManager.IsConnected.Should().BeFalse();
    }

    [Fact]
    public async Task ConnectAsync_WithUnreachableHost_ShouldReturnFalse()
    {
        // Arrange
        var unreachableConfig = new TcpClientConfig
        {
            Host = "*********", // RFC 5737 测试地址，不可达
            Port = 8080,
            ConnectionTimeoutSeconds = 1
        };

        // Act
        var result = await _connectionManager.ConnectAsync("*********", 8080, unreachableConfig);

        // Assert
        result.Should().BeFalse();
        _connectionManager.IsConnected.Should().BeFalse();
    }

    [Fact]
    public async Task DisconnectAsync_WhenNotConnected_ShouldCompleteSuccessfully()
    {
        // Act
        var act = async () => await _connectionManager.DisconnectAsync("Test disconnect");

        // Assert
        await act.Should().NotThrowAsync();
    }

    [Fact]
    public async Task ReconnectAsync_WhenNotConnected_ShouldReturnFalse()
    {
        // Act
        var result = await _connectionManager.ReconnectAsync();

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void EnableAutoReconnect_ShouldEnableAutoReconnectFlag()
    {
        // Act
        _connectionManager.EnableAutoReconnect();

        // Assert - 通过日志验证启用了自动重连
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Debug,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("已启用自动重连")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public void DisableAutoReconnect_ShouldDisableAutoReconnectFlag()
    {
        // Arrange
        _connectionManager.EnableAutoReconnect();

        // Act
        _connectionManager.DisableAutoReconnect();

        // Assert - 通过日志验证禁用了自动重连
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Debug,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("已禁用自动重连")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task RefreshAsync_WhenNotConnected_ShouldCompleteSuccessfully()
    {
        // Act
        var act = async () => await _connectionManager.RefreshAsync();

        // Assert
        await act.Should().NotThrowAsync();
    }

    [Fact]
    public void Dispose_ShouldDisposeResourcesCorrectly()
    {
        // Act
        var act = () => _connectionManager.Dispose();

        // Assert
        act.Should().NotThrow();
    }

    [Fact]
    public async Task DisposeAsync_ShouldDisposeResourcesCorrectly()
    {
        // Act
        var act = async () => await _connectionManager.DisposeAsync();

        // Assert
        await act.Should().NotThrowAsync();
    }

    [Fact]
    public async Task ConnectAsync_AfterDispose_ShouldThrowObjectDisposedException()
    {
        // Arrange
        _connectionManager.Dispose();

        // Act & Assert
        var act = async () => await _connectionManager.ConnectAsync("localhost", 8080, _config);
        await act.Should().ThrowAsync<ObjectDisposedException>();
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    public async Task ConnectAsync_WithEmptyOrWhitespaceHost_ShouldThrowArgumentException(string host)
    {
        // Act & Assert
        var act = async () => await _connectionManager.ConnectAsync(host, 8080, _config);
        await act.Should().ThrowAsync<ArgumentException>();
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-1)]
    [InlineData(65536)]
    public async Task ConnectAsync_WithInvalidPortRange_ShouldReturnFalse(int port)
    {
        // Act
        var result = await _connectionManager.ConnectAsync("localhost", port, _config);

        // Assert
        result.Should().BeFalse();
    }

    public void Dispose()
    {
        _connectionManager?.Dispose();
    }
}
